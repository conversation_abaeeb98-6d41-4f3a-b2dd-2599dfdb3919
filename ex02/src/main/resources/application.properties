spring.application.name=spring

# DB credentials
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres

# FreeMarker configuration
spring.freemarker.template-loader-path=classpath:/templates/
spring.freemarker.suffix=.ftl
spring.freemarker.cache=false
spring.freemarker.charset=UTF-8
spring.freemarker.content-type=text/html

# Encoding configuration
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
spring.messages.encoding=UTF-8

spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgresSQLDialect

#app.upload.path=C:\\Users\\<USER>\\Desktop\\sample-images
app.upload.path=/home/<USER>/uploaded-images

# Multipart file upload configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=5MB
spring.servlet.multipart.file-size-threshold=2KB

server.servlet.session.timeout=10m

# smtp cred
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=kinc figp ydos elhx
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
