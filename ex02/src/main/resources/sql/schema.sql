CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone_number VARCHAR(255) NOT NULL,
    confirmation VARCHAR(255) DEFAULT 'NOT_CONFIRMED',
    role VARCHAR(255) NOT NULL
);

CREATE TABLE films (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL UNIQUE,
    year INTEGER NOT NULL,
    age_restrictions INTEGER NOT NULL,
    description VARCHAR(255) NOT NULL,
    poster_url VARCHAR(255)
);

CREATE TABLE halls (
    id BIGSERIAL PRIMARY KEY,
    serial_number VARCHAR(255) NOT NULL UNIQUE,
    seats INTEGER NOT NULL
);

CREATE TABLE sessions (
    id BIGSERIAL PRIMARY KEY,
    ticket_cost DOUBLE PRECISION NOT NULL,
    session_time TIMESTAMP NOT NULL,
    film_id BIGINT NOT NULL,
    hall_id BIGINT NOT NULL,
    FOREIG<PERSON> KEY (film_id) REFERENCES films(id),
    FOREIGN KEY (hall_id) REFERENCES halls(id)
);

CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    film_id BIGINT,
    sender_id BIGINT,
    content VARCHAR(1000),
    sender_first_name VARCHAR(255),
    user_ip VARCHAR(255),
    timestamp TIMESTAMP
);

CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE,
    ip_address VARCHAR(255),
    login_time TIMESTAMP,
    last_activity TIMESTAMP
);

CREATE TABLE account_confirmation (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    confirmation_code VARCHAR(255) NOT NULL
);

CREATE TABLE images (
    id BIGSERIAL PRIMARY KEY,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    upload_time TIMESTAMP NOT NULL,
    user_id VARCHAR(255) NOT NULL
);

CREATE TABLE persistent_logins (
    username VARCHAR(64) NOT NULL,
    series VARCHAR(64) PRIMARY KEY,
    token VARCHAR(64) NOT NULL,
    last_used TIMESTAMP NOT NULL
);
