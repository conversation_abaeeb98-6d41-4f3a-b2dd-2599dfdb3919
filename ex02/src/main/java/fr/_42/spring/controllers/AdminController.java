package fr._42.spring.controllers;

import fr._42.spring.models.Film;
import fr._42.spring.models.Hall;
import fr._42.spring.models.Session;
import fr._42.spring.services.FilmsService;
import fr._42.spring.services.HallsService;
import fr._42.spring.services.SessionsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Controller()
@RequestMapping("/admin/panel")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {
    @Value("${app.upload.path}")
    private String uploadDirS;

    private final HallsService hallsService;
    private final FilmsService filmsService;
    private final SessionsService sessionsService;
    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);


    @Autowired
    public AdminController(
            HallsService hallsService,
            FilmsService filmsService,
            SessionsService sessionsService
    ) {
        this.filmsService = filmsService;
        this.hallsService = hallsService;
        this.sessionsService = sessionsService;
    }

    @GetMapping(value = {"/halls", "/halls/"})
    public String showHallsPanel(
            RedirectAttributes redirectAttributes,
            Model model) {
        try {
            List<Hall> halls = hallsService.getHalls();
            model.addAttribute("halls", halls);
            return "admin/halls";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
        }
        return "admin/halls";
    }


    @PostMapping(value = {"/halls", "/halls/"})
    public String handleHallsForm(
            @RequestParam String serialNumber,
            @RequestParam int seats,
            RedirectAttributes redirectAttributes
    ) {
        try {
            Hall hall = hallsService.createHall(serialNumber, seats);
            redirectAttributes.addFlashAttribute("success", "Hall created successfully!");
            return "redirect:/admin/panel/halls";
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/admin/panel/halls";
        }
    }


    // handling films actions

    @GetMapping(value = {"/films", "/films/"})
    public String showFilmsPanel(
            RedirectAttributes redirectAttributes,
            Model model
    ) {
        try {
            List<Film> films = filmsService.getFilms();
            model.addAttribute("films", films);
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            model.addAttribute("films", new ArrayList<>());
        }
        return "admin/films";
    }

    @PostMapping(value = {"/films", "/films/"})
    public String handleFilmsForm(
            @RequestParam("title") String title,
            @RequestParam("year") int year,
            @RequestParam("ageRestrictions") int ageRestrictions,
            @RequestParam("description") String description,
            @RequestParam("poster") MultipartFile poster,
            RedirectAttributes redirectAttributes
    ) {
        try {
            String posterUrl = null;
            if (!poster.isEmpty()) {
                try {
                    // Validate file size (5MB max)
                    if (poster.getSize() > 5 * 1024 * 1024) {
                        throw new IllegalArgumentException("File size must be less than 5MB");
                    }

                    // Validate content type
                    String contentType = poster.getContentType();
                    if (contentType == null || !contentType.startsWith("image/")) {
                        throw new IllegalArgumentException("File must be an image");
                    }

                    File uploadDir = new File(uploadDirS);
                    if (!uploadDir.exists()) {
                        uploadDir.mkdirs();
                    }

                    String originalFilename = poster.getOriginalFilename();
                    if (originalFilename == null || originalFilename.trim().isEmpty()) {
                        throw new IllegalArgumentException("Original filename cannot be null or empty");
                    }

                    // Extract file extension with better error handling
                    String extension = "";
                    int lastDotIndex = originalFilename.lastIndexOf('.');
                    if (lastDotIndex > 0 && lastDotIndex < originalFilename.length() - 1) {
                        extension = originalFilename.substring(lastDotIndex);
                    } else {
                        extension = ".jpg"; // Default extension
                    }

                    String uniqueFileName = UUID.randomUUID() + extension;
                    File dest = new File(uploadDir, uniqueFileName);

                    poster.transferTo(dest);
                    posterUrl = uniqueFileName;
                } catch (IOException e) {
                    throw new IllegalArgumentException("Failed to store poster file: " + e.getMessage(), e);
                }
            }
            Film film = new Film(null, title, year, ageRestrictions, description, posterUrl);
            filmsService.addFilm(film);
            redirectAttributes.addFlashAttribute("success", "Film created successfully!");
        } catch (SecurityException e) {
            redirectAttributes.addFlashAttribute("SecurityException: " + e.getMessage());
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
        }
        return "redirect:/admin/panel/films";
    }


    @GetMapping(value = {"/sessions", "/sessions/"})
    public String showSessionsPanel(
            Model model) {
        try {
            List<Session> sessions = sessionsService.getSessions();
            List<Hall> halls = hallsService.getHalls();
            List<Film> films = filmsService.getFilms();

            model.addAttribute("halls", halls);
            model.addAttribute("films", films);
            model.addAttribute("sessions", sessions);
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            model.addAttribute("halls", new ArrayList<>());
            model.addAttribute("films", new ArrayList<>());
            model.addAttribute("sessions", new ArrayList<>());
        }
        return "admin/sessions";
    }

    @PostMapping(value = {"/sessions", "/sessions/"})
    public String handleSessionsForm(
            @RequestParam("filmId") Long filmId,
            @RequestParam("hallId") Long hallId,
            @RequestParam("sessionTime") @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm") LocalDateTime sessionTime,
            @RequestParam("ticketPrice") Double ticketPrice,
            RedirectAttributes redirectAttributes
    ) {
        if (filmId == null || hallId == null || sessionTime == null || ticketPrice == null) {
            redirectAttributes.addFlashAttribute("error", "Please fill in all required fields.");
            return "redirect:/admin/panel/sessions";
        }

        try {
            Film film = filmsService.getFilmById(filmId);
            Hall hall = hallsService.getHallById(hallId);

            Session session = new Session(null, ticketPrice, sessionTime, film, hall);
            sessionsService.addSession(session);

            List<Session> updatedSessions = sessionsService.getSessions();
            redirectAttributes.addFlashAttribute("sessions", updatedSessions);
            redirectAttributes.addFlashAttribute("success", "Session created successfully!");
        } catch (Exception e) {
            redirectAttributes.addAttribute("error", "An error occurred while saving the session into the database");
        }

        return "redirect:/admin/panel/sessions";
    }
}