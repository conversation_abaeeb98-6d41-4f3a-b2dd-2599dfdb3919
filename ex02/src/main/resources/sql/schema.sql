-- Database Schema for Spring Cinema Application
-- This file contains the complete database schema based on JPA entity models

-- Drop tables if they exist (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS sessions CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS account_confirmation CASCADE;
DROP TABLE IF EXISTS images CASCADE;
DROP TABLE IF EXISTS persistent_logins CASCADE;
DROP TABLE IF EXISTS films CASCADE;
DROP TABLE IF EXISTS halls CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone_number VARCHAR(255) NOT NULL,
    confirmation VARCHAR(255) DEFAULT 'NOT_CONFIRMED',
    role VARCHAR(255) NOT NULL,
    
    -- Add constraints for enum values
    CONSTRAINT chk_user_confirmation CHECK (confirmation IN ('CONFIRMED', 'NOT_CONFIRMED')),
    CONSTRAINT chk_user_role CHECK (role IN ('ADMIN', 'USER', 'MODERATOR'))
);

-- Films table  
CREATE TABLE films (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL UNIQUE,
    year INTEGER NOT NULL,
    age_restrictions INTEGER NOT NULL,
    description VARCHAR(255) NOT NULL,
    poster_url VARCHAR(255),
    
    -- Add constraints for reasonable values
    CONSTRAINT chk_film_year CHECK (year >= 1900 AND year <= 2100),
    CONSTRAINT chk_film_age_restrictions CHECK (age_restrictions >= 0 AND age_restrictions <= 18)
);

-- Halls table
CREATE TABLE halls (
    id BIGSERIAL PRIMARY KEY,
    serial_number VARCHAR(255) NOT NULL UNIQUE,
    seats INTEGER NOT NULL,
    
    -- Add constraint for reasonable seat count
    CONSTRAINT chk_hall_seats CHECK (seats > 0 AND seats <= 1000)
);

-- Sessions table (with foreign key relationships)
CREATE TABLE sessions (
    id BIGSERIAL PRIMARY KEY,
    ticket_cost DOUBLE PRECISION NOT NULL,
    session_time TIMESTAMP NOT NULL,
    film_id BIGINT NOT NULL,
    hall_id BIGINT NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_session_film FOREIGN KEY (film_id) REFERENCES films(id) ON DELETE CASCADE,
    CONSTRAINT fk_session_hall FOREIGN KEY (hall_id) REFERENCES halls(id) ON DELETE CASCADE,
    
    -- Add constraint for reasonable ticket cost
    CONSTRAINT chk_session_ticket_cost CHECK (ticket_cost >= 0)
);

-- Chat messages table
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    film_id BIGINT,
    sender_id BIGINT,
    content VARCHAR(1000),
    sender_first_name VARCHAR(255),
    user_ip VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User sessions table
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE,
    ip_address VARCHAR(255),
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Account confirmation table
CREATE TABLE account_confirmation (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    confirmation_code VARCHAR(255) NOT NULL UNIQUE
);

-- Images table
CREATE TABLE images (
    id BIGSERIAL PRIMARY KEY,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    upload_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR(255) NOT NULL
);

-- Persistent logins table (for Spring Security remember-me functionality)
CREATE TABLE persistent_logins (
    username VARCHAR(64) NOT NULL,
    series VARCHAR(64) PRIMARY KEY,
    token VARCHAR(64) NOT NULL,
    last_used TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_films_title ON films(title);
CREATE INDEX idx_films_year ON films(year);
CREATE INDEX idx_sessions_film_id ON sessions(film_id);
CREATE INDEX idx_sessions_hall_id ON sessions(hall_id);
CREATE INDEX idx_sessions_session_time ON sessions(session_time);
CREATE INDEX idx_chat_messages_film_id ON chat_messages(film_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_timestamp ON chat_messages(timestamp);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_account_confirmation_user_id ON account_confirmation(user_id);
CREATE INDEX idx_account_confirmation_code ON account_confirmation(confirmation_code);
CREATE INDEX idx_images_user_id ON images(user_id);
CREATE INDEX idx_persistent_logins_username ON persistent_logins(username);

-- Comments for documentation
COMMENT ON TABLE users IS 'Application users with authentication and profile information';
COMMENT ON TABLE films IS 'Movie catalog with details and age restrictions';
COMMENT ON TABLE halls IS 'Cinema halls with seating capacity';
COMMENT ON TABLE sessions IS 'Movie screening sessions linking films to halls with scheduling';
COMMENT ON TABLE chat_messages IS 'Chat messages for film discussions';
COMMENT ON TABLE user_sessions IS 'Active user session tracking';
COMMENT ON TABLE account_confirmation IS 'Email confirmation codes for new user accounts';
COMMENT ON TABLE images IS 'Uploaded image files metadata';
COMMENT ON TABLE persistent_logins IS 'Spring Security remember-me token storage';
